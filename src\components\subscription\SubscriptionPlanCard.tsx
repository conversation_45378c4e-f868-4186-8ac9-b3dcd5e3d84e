import React from 'react';
import { SubscriptionPlan } from '../../types';
import Button from '../ui/button/Button';

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isCurrentPlan?: boolean;
  isRecommended?: boolean;
  isLoading?: boolean;
  onSubscribe?: (planId: string) => void;
  onUpgrade?: (planId: string) => void;
  className?: string;
  type?: 'vertical' | 'horizontal';
}

export default function SubscriptionPlanCard({
  plan,
  isCurrentPlan = false,
  isRecommended = false,
  isLoading = false,
  onSubscribe,
  onUpgrade,
  className = '',
}: SubscriptionPlanCardProps) {
  
  const handleActionClick = () => {
    if (isCurrentPlan) return;
    
    if (onUpgrade && !plan.isOneTime) {
      onUpgrade(plan.id);
    } else if (onSubscribe) {
      onSubscribe(plan.id);
    }
  };

  const getActionButtonText = () => {
    if (isCurrentPlan) return 'Current Plan';
    if (plan.isOneTime) return 'Purchase Credits';
    if (plan.id === 'free') return 'Get Started';
    return 'Subscribe';
  };

  const getActionButtonVariant = () => {
    if (isCurrentPlan) return 'outline';
    return 'primary';
  };

  const getPlanTypeIcon = () => {
    if (plan.isOneTime) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      );
    }
    return (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  };

  const getCreditsDisplay = () => {
    if (plan.effect.kind === 'credits') {
      return `${plan.effect.amount} Credits`;
    }
    return `${plan.effect.amount} Credits/month`;
  };

  const getQueuesDisplay = () => {
    if (plan.effect.queues === null) return null;
    return `${plan.effect.queues} Queue${plan.effect.queues !== 1 ? 's' : ''}`;
  };

  return (
    <div 
      className={`
        relative bg-white dark:bg-gray-800 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg
        ${isCurrentPlan 
          ? 'border-brand-500 bg-brand-50/50 dark:bg-brand-900/10' 
          : isRecommended 
            ? 'border-brand-300 dark:border-brand-600' 
            : 'border-gray-200 dark:border-gray-700 hover:border-brand-200 dark:hover:border-brand-700'
        }
        ${className}
      `}
    >
      {/* Recommended Badge */}
      {isRecommended && !isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-brand-500 text-white">
            Most Popular
          </span>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
            Current Plan
          </span>
        </div>
      )}

      <div className="p-6">
        {/* Plan Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-3">
            <div className={`p-2 rounded-lg ${isCurrentPlan ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'}`}>
              {getPlanTypeIcon()}
            </div>
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {plan.name}
          </h3>
          
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {plan.description}
          </p>

          {/* Price */}
          <div className="mb-4">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">
              {plan.price}
            </span>
            {plan.isSubscription && plan.id !== 'free' && (
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                /month
              </span>
            )}
          </div>

          {/* Credits and Queues Info */}
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
            <div className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span>{getCreditsDisplay()}</span>
            </div>
            
            {getQueuesDisplay() && (
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span>{getQueuesDisplay()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Features List */}
        <div className="mb-6">
          <ul className="space-y-3">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {feature}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Action Button */}
        <Button
          onClick={handleActionClick}
          variant={getActionButtonVariant()}
          size="md"
          disabled={isCurrentPlan || isLoading}
          className="w-full"
        >
          {isLoading ? 'Processing...' : getActionButtonText()}
        </Button>

        {/* Plan Type Indicator */}
        <div className="mt-4 text-center">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            plan.isOneTime 
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
              : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
          }`}>
            {plan.isOneTime ? 'One-time Purchase' : 'Monthly Subscription'}
          </span>
        </div>
      </div>
    </div>
  );
}
